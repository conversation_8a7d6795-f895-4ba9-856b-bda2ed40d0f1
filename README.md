# Athena Book Selling System

A modern, responsive book selling platform built with React, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Modern Tech Stack**: React 19, TypeScript, Vite
- **Responsive Design**: Tailwind CSS with shadcn/ui components
- **Smooth Animations**: Framer Motion for enhanced UX
- **Icon Library**: React Icons for consistent iconography
- **Routing**: React Router DOM for navigation
- **HTTP Client**: Axios for API communication
- **Type Safety**: Full TypeScript support
- **Clean Architecture**: Well-organized project structure

## 📦 Dependencies

### Core Dependencies

- **React 19** - Modern React with latest features
- **TypeScript** - Type safety and better developer experience
- **Vite** - Fast build tool and development server
- **React Router DOM** - Client-side routing
- **Axios** - HTTP client for API calls
- **Framer Motion** - Animation library
- **React Icons** - Icon library

### UI & Styling

- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - High-quality, accessible UI components
- **class-variance-authority** - Component variant management
- **clsx** - Conditional className utility
- **tailwind-merge** - Tailwind class merging utility

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui components
│   └── layout/         # Layout components
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── services/           # API services and external integrations
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── context/            # React context providers
├── lib/                # Library configurations and utilities
└── assets/             # Static assets
```

## 🛠️ Setup Instructions

### Prerequisites

- Node.js (v18 or higher)
- pnpm (recommended) or npm

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd athena-book-selling-system
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your configuration values.

4. **Start development server**

   ```bash
   pnpm dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:5173`

## 🎨 Styling & Components

### Tailwind CSS Configuration

- Custom color palette with CSS variables
- Dark mode support
- Responsive design utilities
- Custom animations and keyframes

### shadcn/ui Integration

- Pre-configured with optimal settings
- Custom utility functions in `src/lib/utils.ts`
- Component variants using class-variance-authority
- Accessible and customizable components

## 🔧 Development

### Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm preview` - Preview production build
- `pnpm lint` - Run ESLint

### Code Quality

- ESLint configuration for React and TypeScript
- Strict TypeScript configuration
- Consistent code formatting
- Path aliases configured (`@/` for `src/`)

## 📱 Features Implementation

### Cart Management

- Custom `useCart` hook for state management
- Local storage persistence
- Add, remove, and update quantities
- Real-time total calculations

### API Integration

- Axios instance with interceptors
- Authentication token handling
- Error handling and retry logic
- Environment-based configuration

### Type Safety

- Comprehensive TypeScript interfaces
- API response types
- Form validation types
- Component prop types

## 🎯 Next Steps

1. **Add Authentication**

   - Login/Register pages
   - JWT token management
   - Protected routes

2. **Implement Book Catalog**

   - Book listing page
   - Search and filtering
   - Category navigation
   - Book details page

3. **Shopping Cart & Checkout**

   - Cart page implementation
   - Checkout process
   - Payment integration
   - Order management

4. **Admin Panel**
   - Book management
   - Order tracking
   - User management
   - Analytics dashboard

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
