import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FiBook, FiShoppingCart, FiStar } from "react-icons/fi";

const Home = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Welcome to <span className="text-blue-600">Athena</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Discover your next favorite book from our extensive collection. From
            bestsellers to hidden gems, we have something for every reader.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" className="flex items-center gap-2">
              <FiBook className="w-5 h-5" />
              Browse Books
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="flex items-center gap-2"
            >
              <FiShoppingCart className="w-5 h-5" />
              View Cart
            </Button>
          </div>
        </motion.div>

        {/* Features Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid md:grid-cols-3 gap-8 mb-16"
        >
          <Card className="text-center">
            <CardHeader>
              <FiBook className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <CardTitle>Vast Collection</CardTitle>
              <CardDescription>
                Thousands of books across all genres and categories
              </CardDescription>
            </CardHeader>
          </Card>
          <Card className="text-center">
            <CardHeader>
              <FiShoppingCart className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <CardTitle>Easy Shopping</CardTitle>
              <CardDescription>
                Simple and secure checkout process with multiple payment options
              </CardDescription>
            </CardHeader>
          </Card>
          <Card className="text-center">
            <CardHeader>
              <FiStar className="w-12 h-12 text-yellow-600 mx-auto mb-4" />
              <CardTitle>Quality Service</CardTitle>
              <CardDescription>
                Fast delivery and excellent customer support
              </CardDescription>
            </CardHeader>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Home;
